<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>500 - Internal Server Error</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .error-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            padding: 60px 40px;
            text-align: center;
            max-width: 600px;
            width: 90%;
            position: relative;
            overflow: hidden;
        }

        .error-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
        }

        .error-icon {
            font-size: 120px;
            color: #ff6b6b;
            margin-bottom: 30px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .error-code {
            font-size: 72px;
            font-weight: bold;
            color: #ff6b6b;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .error-title {
            font-size: 32px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
        }

        .error-message {
            font-size: 18px;
            color: #7f8c8d;
            line-height: 1.6;
            margin-bottom: 40px;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }

        .error-details {
            background: #f8f9fa;
            border-left: 4px solid #ff6b6b;
            padding: 20px;
            margin: 30px 0;
            border-radius: 5px;
            text-align: left;
        }

        .error-details h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 16px;
        }

        .error-details ul {
            color: #7f8c8d;
            padding-left: 20px;
        }

        .error-details li {
            margin-bottom: 5px;
        }

        .dashboard-btn {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 40px;
            border: none;
            border-radius: 50px;
            font-size: 18px;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .dashboard-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
        }

        .dashboard-btn:active {
            transform: translateY(-1px);
        }

        .dashboard-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .dashboard-btn:hover::before {
            left: 100%;
        }

        .additional-info {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid #ecf0f1;
        }

        .contact-info {
            color: #95a5a6;
            font-size: 14px;
            margin-top: 20px;
        }

        .contact-info a {
            color: #667eea;
            text-decoration: none;
        }

        .contact-info a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .error-container {
                padding: 40px 20px;
                margin: 20px;
            }

            .error-code {
                font-size: 48px;
            }

            .error-title {
                font-size: 24px;
            }

            .error-message {
                font-size: 16px;
            }

            .dashboard-btn {
                padding: 12px 30px;
                font-size: 16px;
            }
        }

        .loading-animation {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
            display: none;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">⚠️</div>
        <div class="error-code">500</div>
        <h1 class="error-title">Internal Server Error</h1>
        <p class="error-message">
            Oops! Something went wrong on our server. We're experiencing technical difficulties and our team has been notified. Please try again in a few moments.
        </p>

        <div class="error-details">
            <h4>What happened?</h4>
            <ul>
                <li>The server encountered an unexpected condition</li>
                <li>This could be due to a temporary server overload</li>
                <li>Database connection issues</li>
                <li>Application configuration problems</li>
            </ul>
        </div>

        <div class="additional-info">
            <a href="../dashboard.php" class="dashboard-btn" id="dashboardBtn">
                🏠 Return to Dashboard
                <div class="loading-animation" id="loadingSpinner"></div>
            </a>
        </div>

        <div class="contact-info">
            <p>If the problem persists, please contact our support team at <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p>Error ID: <?php echo uniqid('ERR_'); ?> | Time: <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>
    </div>

    <script>
        // Add loading animation when dashboard button is clicked
        document.getElementById('dashboardBtn').addEventListener('click', function(e) {
            const spinner = document.getElementById('loadingSpinner');
            const btn = this;
            
            // Show loading spinner
            spinner.style.display = 'inline-block';
            btn.style.pointerEvents = 'none';
            btn.style.opacity = '0.8';
            
            // Add a small delay to show the loading effect
            setTimeout(() => {
                window.location.href = btn.href;
            }, 500);
            
            // Prevent default to control the navigation
            e.preventDefault();
        });

        // Auto-refresh page after 30 seconds
        setTimeout(() => {
            if (confirm('Would you like to try refreshing the page?')) {
                window.location.reload();
            }
        }, 30000);

        // Log error for debugging (if console is available)
        if (typeof console !== 'undefined') {
            console.error('500 Internal Server Error - Page loaded at:', new Date().toISOString());
        }
    </script>
</body>
</html>
