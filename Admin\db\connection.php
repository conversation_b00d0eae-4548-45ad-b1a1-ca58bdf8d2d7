<?php
$Status = "Disconnected";

define("DB_HOST", "localhost");
define("DB_USER", "root");
define("DB_PASS", "");
define("DB_NAME", "ssasit");

$conn = mysqli_connect(DB_HOST, DB_USER, DB_PASS, DB_NAME);
if (!$conn) {
    // die("Connection failed" . mysqli_connect_error());
    exit();
}
$Status = "Connected";
echo $Status;


$FirstName = "pritesh";
$LastName = "godavariya";
$DOB = "2000-01-01";
$Address = '6334 nilkanth';
$City = 'ahmedabad';
$Mobile = '9876543210';
$Email = '<EMAIL>';
$Gender = 'male';
$Qualification = '12th';
$Photo = 'SSASIT.png';
$Department = '1';
$Admission_Date = '2020-01-01';
$PersonStatus = 'active';

$insert = "INSERT INTO person VALUES ('', '$FirstName', '$LastName', '$DOB', '$Address', '$City', '$Mobile', '$Email', '$Gender', '$Qualification', '$Photo', '$Department', '$Admission_Date', '$PersonStatus')";


    $result = mysqli_query($conn, $insert);
if ($result) {
    echo "Data Inserted Successfully";
} else {
    echo "Data Not Inserted. Error: " . mysqli_error($conn);
}
// echo insert($conn, $data, $entity_type);

?>