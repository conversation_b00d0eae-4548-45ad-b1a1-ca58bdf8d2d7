<?php
$Status = "Disconnected";
define("DB_HOST", "localhost");
define("DB_USER", "root");
define("DB_PASS", "");
define("DB_NAME", "ssasit");

$conn = mysqli_connect(DB_HOST, DB_USER, DB_PASS, DB_NAME);
if (!$conn) {
    // die("Connection failed" . mysqli_connect_error());
    exit();
}
$Status = "Connected";
echo $Status;

require '../Querys/Insert Data.php';
$entity_type = 'student';

    $FirstName = "pritesh";
    $LastName = "godavariya";
    $DOB = "2000-01-01";
    $Address = '6334 nilkanth';
    $City = 'ahmedabad';
    $Mobile = '9876543210';
    $Email = '<EMAIL>';
    $Gender = 'male';
    $Qualification = '12th';
    $Photo = 'SSASIT.png';
    $Department = '1';
    $Admission_Date = '2020-01-01';
    $Status = 'active';

    $insert = "insert into person values('" . $FirstName . "','" . $LastName . "','" . $DOB . "','" . $Address . "','" . $City . "','" . $Mobile . "','" . $Email . "','" . $Gender . "','" . $Qualification . "','" . $Photo . "','" . $Department . "','" . $Admission_Date . "','" . $Status . "')";


        $result = mysqli_query($conn, $insert);
        if ($result) {
            echo "Data Inserted";
        } else {
            echo "Data Not Inserted";
        }
// echo insert($conn, $data, $entity_type);

?>