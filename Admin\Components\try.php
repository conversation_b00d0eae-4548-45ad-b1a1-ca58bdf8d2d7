<?php
require 'db/connection.php';
require 'Querys/Insert Data.php';

$entity_type = 'student';
echo 'hello';
// if (isset($_POST['btn'])) {
$data = [
    "First Name" => $_POST['firstName'],
    "Last Name" => $_POST['lastName'],
    "Date of Birth" => $_POST['dob'],
    "Address" => $_POST['address'],
    "City" => $_POST['city'],
    "Mobile" => $_POST['mobile'],
    "Email" => $_POST['email'],
    "Gender" => $_POST['gender'],
    "Qualification" => $_POST['qualification'],
    "Photo" => 'SSASIT.png',
    "Department" => $_POST['department'],
    "Status" => $_POST['status'],
    ($entity_type == 'faculty') ? "Joining Date" : "Admission Date" => $_POST['admissionDate'],
    "Designation" => ($entity_type == 'student') ? "Student" : $_POST['designation'],

];

$result = insert($conn, $data, $entity_type);
if ($result) {
    echo "inserted";
} else {
    echo $result;
}
// }
?>