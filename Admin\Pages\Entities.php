<?php
require 'Services/Verify-User.php';
require 'db/connection.php';
require 'Querys/Insert Data.php';

$entity_type = isset($_GET['type']) ? $_GET['type'] : 'student';


if (isset($Status) && $Status !== 'Connected') {
    http_response_code(500);
    require 'Pages/500.html';
    exit();
}
if (isset($_POST['submit'])) {
    $data = [
        "First Name" => $_POST['firstName'],
        "Last Name" => $_POST['lastName'],
        "Date of Birth" => $_POST['dob'],
        "Address" => $_POST['address'],
        "City" => $_POST['city'],
        "Mobile" => $_POST['mobile'],
        "Email" => $_POST['email'],
        "Gender" => $_POST['gender'],
        "Qualification" => $_POST['qualification'],
        "Photo" => $_FILES['photo'],
        "Department" => $_POST['department'],
        "Status" => $_POST['status'],
        ($entity_type == 'faculty') ? "Joining Date" : "Admission Date" => $_POST['admissionDate'],
    ];

    // $Insert = "insert into person values('','" . $data['First Name'] . "','" . $data['Last Name'] . "','" . $data['Date of Birth'] . "','" . $data['Address'] . "','" . $data['City'] . "','" . $data['Mobile'] . "','" . $data['Email'] . "','" . $data['Gender'] . "','" . $data['Qualification'] . "','" . $data['Photo'] . "','" . $data['Department'] . "','" . $data[($entity_type == 'faculty') ? "Joining Date" : "Admission Date"] . "','" . $data['Status'] . "')";

    $result = insert($conn, $data,$entity_type);

    if ($result) {
        header('Location: Dashboard');
    } else {
        echo "Data Not Inserted";
    }

}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Student - SSASIT Admin</title>
    <?php require 'config/Icon-Config.php'; ?>
    <link rel="stylesheet" href="index.css?v=<?php echo time(); ?>">
    <!-- <link rel="stylesheet" href="Css/.css?v=<?php echo time(); ?>"> -->
</head>
<style>
    body {
        font-family: var(--primary-font);
        background: var(--bg-gradient);
        min-height: 100vh;
        /* padding: 1.25rem; */
    }
</style>

<body>
    <?php require 'Components/Header.php'; ?>
    <?php require 'Components/Entities-Form.php'; ?>
</body>
<script>
</script>

</html>