<?php
require 'Services/Verify-User.php';
require 'db/connection.php';
require 'Querys/Insert Data.php';

$entity_type = isset($_GET['type']) ? $_GET['type'] : 'student';


if (isset($Status) && $Status !== 'Connected') {
    http_response_code(500);
    require 'Pages/500.html';
    exit();
}
if (isset($_POST['btn'])) {
    $data = [
        "First Name" => $_POST['firstName'],
        "Last Name" => $_POST['lastName'],
        "Date of Birth" => $_POST['dob'],
        "Address" => $_POST['address'],
        "City" => $_POST['city'],
        "Mobile" => $_POST['mobile'],
        "Email" => $_POST['email'],
        "Gender" => $_POST['gender'],
        "Qualification" => $_POST['qualification'],
        "Photo" => 'SSASIT.png',
        "Department" => $_POST['department'],
        "Status" => $_POST['status'],
        ($entity_type == 'faculty') ? "Joining Date" : "Admission Date" => $_POST['admissionDate'],
        "Designation" => ($entity_type == 'student') ? "Student" : $_POST['designation'],

    ];
    $result = insert($conn, $data, $entity_type);
    if ($result) {
        header('Location: Dashboard');
    } else {
        echo $result;
    }
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Student - SSASIT Admin</title>
    <?php require 'config/Icon-Config.php'; ?>
    <link rel="stylesheet" href="index.css?v=<?php echo time(); ?>">
    <!-- <link rel="stylesheet" href="Css/.css?v=<?php echo time(); ?>"> -->
</head>
<style>
    body {
        font-family: var(--primary-font);
        background: var(--bg-gradient);
        min-height: 100vh;
        /* padding: 1.25rem; */
    }
</style>

<body>
    <?php require 'Components/Header.php'; ?>
    <?php require 'Components/Entities-Form.php'; ?>
</body>
<script>
</script>

</html>