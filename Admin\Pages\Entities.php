<?php
require 'Services/Verify-User.php';

$entity_type = isset($_GET['type']) ? $_GET['type'] : 'student';
$status = isset($_GET['status']) ? $_GET['status'] : '';
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Student - SSASIT Admin</title>
    <?php require 'config/Icon-Config.php'; ?>
    <link rel="stylesheet" href="index.css?v=<?php echo time(); ?>">
    <!-- <link rel="stylesheet" href="Css/.css?v=<?php echo time(); ?>"> -->
</head>
<style>
    body {
        font-family: var(--primary-font);
        background: var(--bg-gradient);
        min-height: 100vh;
        /* padding: 1.25rem; */
    }

    .success-message,
    .field-error {
        position: fixed;
        top: 5rem;
        right: 1.2rem;
        background: var(--success-color);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.6rem;
        font-family: var(--primary-font);
        font-weight: 600;
        z-index: 1000;
        animation: slideIn 0.3s ease;
        transition: all .3s ease;
    }

    .field-error {
        background: var(--error-color);
    }

    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateX(100%);
        }

        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
</style>

<body>
    <?php if ($status === 'success') { ?>
        <div class="success-message">Record Added Successfully</div>
    <?php } ?>
    <?php if ($status === 'field') { ?>
        <div class="field-error">Record Not Inserted</div>
    <?php } ?>
    <?php require 'Components/Header.php'; ?>
    <?php require 'Components/Entities-Form.php'; ?>
</body>
<script>
    const successMessage = document.querySelector('.success-message');
    if (successMessage) {
        setTimeout(() => {
            successMessage.remove();
        }, 3000);
    }
    // function showSuccessMessage(message) {
    //     const successDiv = document.createElement('div');
    //     successDiv.style.cssText = `
    //             position: fixed;
    //             top: 20px;
    //             right: 20px;
    //             background: var(--success-color);
    //             color: white;
    //             padding: 1rem 1.5rem;
    //             border-radius: 0.6rem;
    //             font-family: var(--primary-font);
    //             font-weight: 600;
    //             z-index: 1000;
    //             animation: slideIn 0.3s ease;
    //         `;
    //     successDiv.textContent = message;
    //     document.body.appendChild(successDiv);

    //     setTimeout(() => {
    //         successDiv.remove();
    //     }, 3000);
    // }
    // const style = document.createElement('style');
    // style.textContent = `
    //         @keyframes slideIn {
    //             from {
    //                 opacity: 0;
    //                 transform: translateX(100%);
    //             }
    //             to {
    //                 opacity: 1;
    //                 transform: translateX(0);
    //             }
    //         }
    //     `;
    // document.head.appendChild(style);

</script>

</html>