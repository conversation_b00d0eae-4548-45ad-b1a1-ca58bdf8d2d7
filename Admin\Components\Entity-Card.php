<style>
    .entity-card {
        background: #ffffff;
        border-radius: 20px;
        padding: 0;
        margin: 16px;
        max-width: 280px;
        border: 1px solid rgba(226, 232, 240, 0.6);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    }

    .entity-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #4F46E5, #7C3AED);
        transform: scaleX(0);
        transition: transform 0.4s ease;
        z-index: 1;
    }

    .entity-card:hover {
        transform: translateY(-12px);
        box-shadow: 0 25px 50px rgba(79, 70, 229, 0.15);
        border-color: rgba(79, 70, 229, 0.2);
    }

    .entity-card:hover::before {
        transform: scaleX(1);
    }

    .card-content {
        padding: 24px;
        text-align: center;
        position: relative;
        z-index: 2;
    }

    .profile-image {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        object-fit: cover;
        margin: 0 auto 16px;
        border: 3px solid #ffffff;
        box-shadow: 0 8px 24px rgba(79, 70, 229, 0.15);
        transition: all 0.3s ease;
        display: block;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 18px;
    }

    .entity-card:hover .profile-image {
        transform: scale(1.1);
        box-shadow: 0 12px 32px rgba(79, 70, 229, 0.25);
    }

    .entity-name {
        font-size: 18px;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 6px;
        transition: color 0.3s ease;
    }

    .entity-id {
        font-size: 13px;
        color: #64748b;
        margin-bottom: 20px;
        font-weight: 500;
        background: #f1f5f9;
        padding: 4px 12px;
        border-radius: 12px;
        display: inline-block;
    }

    .card-actions {
        display: flex;
        gap: 8px;
        justify-content: center;
        opacity: 0;
        transform: translateY(10px);
        transition: all 0.3s ease;
    }

    .entity-card:hover .card-actions {
        opacity: 1;
        transform: translateY(0);
    }

    .action-btn {
        padding: 8px 16px;
        border: none;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 4px;
        backdrop-filter: blur(10px);
        border: 1px solid transparent;
    }

    .btn-view {
        background: rgba(59, 130, 246, 0.1);
        color: #2563eb;
        border-color: rgba(59, 130, 246, 0.2);
    }

    .btn-view:hover {
        background: rgba(59, 130, 246, 0.2);
        transform: translateY(-2px);
    }

    .btn-edit {
        background: rgba(16, 185, 129, 0.1);
        color: #059669;
        border-color: rgba(16, 185, 129, 0.2);
    }

    .btn-edit:hover {
        background: rgba(16, 185, 129, 0.2);
        transform: translateY(-2px);
    }

    .btn-delete {
        background: rgba(239, 68, 68, 0.1);
        color: #dc2626;
        border-color: rgba(239, 68, 68, 0.2);
    }

    .btn-delete:hover {
        background: rgba(239, 68, 68, 0.2);
        transform: translateY(-2px);
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .entity-card {
        animation: slideInUp 0.6s ease-out;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .entity-cards-container {
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1rem;
            padding: 1rem;
        }

        .entity-card {
            border-radius: 0.75rem;
        }

        .card-header {
            padding: 0.75rem;
        }

        .card-avatar {
            width: 70px;
            height: 70px;
        }

        .card-body {
            padding: 1rem;
        }

        .entity-name {
            font-size: 1.1rem;
        }

        .entity-id {
            font-size: 0.9rem;
        }

        .card-actions {
            padding: 0 1rem 1rem;
            gap: 0.4rem;
        }

        .action-btn {
            padding: 0.5rem 0.75rem;
            font-size: 0.8rem;
        }
    }

    @media (max-width: 480px) {
        .entity-cards-container {
            grid-template-columns: 1fr;
            gap: 0.75rem;
            padding: 0.75rem;
        }

        .card-actions {
            flex-direction: column;
            gap: 0.5rem;
        }

        .action-btn {
            flex: none;
            padding: 0.6rem 1rem;
        }
    }

    /* Loading Animation */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Icons for buttons */
    .btn-icon {
        width: 16px;
        height: 16px;
        fill: currentColor;
    }
</style>

<div class="entity-cards-container">
    <!-- Sample Student Card -->
    <div class="entity-card">
        <div class="card-header">
            <div class="entity-type">Student</div>
            <img src="https://via.placeholder.com/80x80/036ce2/ffffff?text=JS" alt="Student Photo" class="card-avatar">
            <div class="status-badge">Active</div>
        </div>
        <div class="card-body">
            <h3 class="entity-name">John Smith</h3>
            <div class="entity-id">ENR-2024-001</div>
        </div>
        <div class="card-actions">
            <button class="action-btn btn-view" onclick="viewEntity('student', 1)">
                <svg class="btn-icon" viewBox="0 0 24 24">
                    <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                </svg>
                View
            </button>
            <button class="action-btn btn-edit" onclick="editEntity('student', 1)">
                <svg class="btn-icon" viewBox="0 0 24 24">
                    <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                </svg>
                Edit
            </button>
            <button class="action-btn btn-delete" onclick="deleteEntity('student', 1)">
                <svg class="btn-icon" viewBox="0 0 24 24">
                    <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                </svg>
                Delete
            </button>
        </div>
    </div>

    <!-- Sample Faculty Card -->
    <div class="entity-card">
        <div class="card-header">
            <div class="entity-type">Faculty</div>
            <div class="card-avatar placeholder">DP</div>
            <div class="status-badge">Active</div>
        </div>
        <div class="card-body">
            <h3 class="entity-name">Dr. Priya Sharma</h3>
            <div class="entity-id">EMP-2023-045</div>
        </div>
        <div class="card-actions">
            <button class="action-btn btn-view" onclick="viewEntity('faculty', 2)">
                <svg class="btn-icon" viewBox="0 0 24 24">
                    <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                </svg>
                View
            </button>
            <button class="action-btn btn-edit" onclick="editEntity('faculty', 2)">
                <svg class="btn-icon" viewBox="0 0 24 24">
                    <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                </svg>
                Edit
            </button>
            <button class="action-btn btn-delete" onclick="deleteEntity('faculty', 2)">
                <svg class="btn-icon" viewBox="0 0 24 24">
                    <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                </svg>
                Delete
            </button>
        </div>
    </div>

    <!-- Sample Graduated Student Card -->
    <div class="entity-card">
        <div class="card-header">
            <div class="entity-type">Student</div>
            <img src="https://via.placeholder.com/80x80/28a745/ffffff?text=AM" alt="Student Photo" class="card-avatar">
            <div class="status-badge status-graduated">Graduated</div>
        </div>
        <div class="card-body">
            <h3 class="entity-name">Alice Miller</h3>
            <div class="entity-id">ENR-2020-156</div>
        </div>
        <div class="card-actions">
            <button class="action-btn btn-view" onclick="viewEntity('student', 3)">
                <svg class="btn-icon" viewBox="0 0 24 24">
                    <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                </svg>
                View
            </button>
            <button class="action-btn btn-edit" onclick="editEntity('student', 3)">
                <svg class="btn-icon" viewBox="0 0 24 24">
                    <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                </svg>
                Edit
            </button>
            <button class="action-btn btn-delete" onclick="deleteEntity('student', 3)">
                <svg class="btn-icon" viewBox="0 0 24 24">
                    <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                </svg>
                Delete
            </button>
        </div>
    </div>
</div>

<script>
    // Placeholder functions for button actions - add your logic here later
    function viewEntity(type, id) {
        console.log(`View ${type} with ID: ${id}`);
        // Add your view logic here
    }

    function editEntity(type, id) {
        console.log(`Edit ${type} with ID: ${id}`);
        // Add your edit logic here
    }

    function deleteEntity(type, id) {
        console.log(`Delete ${type} with ID: ${id}`);
        // Add your delete logic here
    }
</script>
