<style>
    /* Entity Card Styles */
    .entity-cards-container {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: 1.5rem;
        padding: 2rem;
        max-width: 1400px;
        margin: 0 auto;
    }

    .entity-card {
        background: var(--white);
        border-radius: 1rem;
        box-shadow: var(--shadow);
        overflow: hidden;
        transition: var(--transition);
        position: relative;
        border: 1px solid var(--border-color);
        animation: fadeInUp 0.6s ease-out;
    }

    .entity-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-focus);
        border-color: var(--secondary-color);
    }

    .card-header {
        background: var(--header-gradient);
        padding: 1rem;
        text-align: center;
        position: relative;
    }

    .card-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        border: 4px solid var(--white);
        object-fit: cover;
        margin: 0 auto;
        display: block;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        background: var(--white);
    }

    .card-avatar.placeholder {
        background: var(--primary-color);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        color: var(--secondary-color);
        font-weight: bold;
    }

    .card-body {
        padding: 1.5rem;
        text-align: center;
    }

    .entity-name {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-color);
        margin-bottom: 0.5rem;
        text-transform: capitalize;
    }

    .entity-id {
        font-size: 0.95rem;
        color: var(--secondary-color);
        font-weight: 500;
        margin-bottom: 1rem;
        padding: 0.25rem 0.75rem;
        background: var(--primary-color);
        border-radius: 1rem;
        display: inline-block;
    }

    .entity-type {
        position: absolute;
        top: 0.75rem;
        right: 0.75rem;
        background: rgba(255, 255, 255, 0.9);
        color: var(--secondary-color);
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: capitalize;
        backdrop-filter: blur(10px);
    }

    .card-actions {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
        padding: 0 1.5rem 1.5rem;
    }

    .action-btn {
        flex: 1;
        padding: 0.6rem 1rem;
        border: none;
        border-radius: 0.5rem;
        font-size: 0.85rem;
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition);
        text-transform: capitalize;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.4rem;
        min-height: 36px;
    }

    .btn-view {
        background: var(--button-gradient);
        color: var(--white);
        box-shadow: 0 2px 8px rgba(3, 108, 226, 0.3);
    }

    .btn-view:hover {
        background: var(--header-gradient);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(3, 108, 226, 0.4);
    }

    .btn-edit {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: var(--white);
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
    }

    .btn-edit:hover {
        background: linear-gradient(135deg, #218838, #1e7e34);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
    }

    .btn-delete {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: var(--white);
        box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
    }

    .btn-delete:hover {
        background: linear-gradient(135deg, #c82333, #bd2130);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
    }

    .action-btn:active {
        transform: translateY(0);
    }

    /* Status Badge */
    .status-badge {
        position: absolute;
        top: -5px;
        left: 50%;
        transform: translateX(-50%);
        background: var(--success-color);
        color: var(--white);
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .status-inactive {
        background: #6c757d;
    }

    .status-graduated {
        background: #17a2b8;
    }

    .status-suspended {
        background: #ffc107;
        color: var(--text-color);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .entity-cards-container {
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1rem;
            padding: 1rem;
        }

        .entity-card {
            border-radius: 0.75rem;
        }

        .card-header {
            padding: 0.75rem;
        }

        .card-avatar {
            width: 70px;
            height: 70px;
        }

        .card-body {
            padding: 1rem;
        }

        .entity-name {
            font-size: 1.1rem;
        }

        .entity-id {
            font-size: 0.9rem;
        }

        .card-actions {
            padding: 0 1rem 1rem;
            gap: 0.4rem;
        }

        .action-btn {
            padding: 0.5rem 0.75rem;
            font-size: 0.8rem;
        }
    }

    @media (max-width: 480px) {
        .entity-cards-container {
            grid-template-columns: 1fr;
            gap: 0.75rem;
            padding: 0.75rem;
        }

        .card-actions {
            flex-direction: column;
            gap: 0.5rem;
        }

        .action-btn {
            flex: none;
            padding: 0.6rem 1rem;
        }
    }

    /* Loading Animation */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Icons for buttons */
    .btn-icon {
        width: 16px;
        height: 16px;
        fill: currentColor;
    }
</style>

<div class="entity-cards-container">
    <!-- Sample Student Card -->
    <div class="entity-card">
        <div class="card-header">
            <div class="entity-type">Student</div>
            <img src="https://via.placeholder.com/80x80/036ce2/ffffff?text=JS" alt="Student Photo" class="card-avatar">
            <div class="status-badge">Active</div>
        </div>
        <div class="card-body">
            <h3 class="entity-name">John Smith</h3>
            <div class="entity-id">ENR-2024-001</div>
        </div>
        <div class="card-actions">
            <button class="action-btn btn-view" onclick="viewEntity('student', 1)">
                <svg class="btn-icon" viewBox="0 0 24 24">
                    <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                </svg>
                View
            </button>
            <button class="action-btn btn-edit" onclick="editEntity('student', 1)">
                <svg class="btn-icon" viewBox="0 0 24 24">
                    <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                </svg>
                Edit
            </button>
            <button class="action-btn btn-delete" onclick="deleteEntity('student', 1)">
                <svg class="btn-icon" viewBox="0 0 24 24">
                    <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                </svg>
                Delete
            </button>
        </div>
    </div>

    <!-- Sample Faculty Card -->
    <div class="entity-card">
        <div class="card-header">
            <div class="entity-type">Faculty</div>
            <div class="card-avatar placeholder">DP</div>
            <div class="status-badge">Active</div>
        </div>
        <div class="card-body">
            <h3 class="entity-name">Dr. Priya Sharma</h3>
            <div class="entity-id">EMP-2023-045</div>
        </div>
        <div class="card-actions">
            <button class="action-btn btn-view" onclick="viewEntity('faculty', 2)">
                <svg class="btn-icon" viewBox="0 0 24 24">
                    <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                </svg>
                View
            </button>
            <button class="action-btn btn-edit" onclick="editEntity('faculty', 2)">
                <svg class="btn-icon" viewBox="0 0 24 24">
                    <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                </svg>
                Edit
            </button>
            <button class="action-btn btn-delete" onclick="deleteEntity('faculty', 2)">
                <svg class="btn-icon" viewBox="0 0 24 24">
                    <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                </svg>
                Delete
            </button>
        </div>
    </div>

    <!-- Sample Graduated Student Card -->
    <div class="entity-card">
        <div class="card-header">
            <div class="entity-type">Student</div>
            <img src="https://via.placeholder.com/80x80/28a745/ffffff?text=AM" alt="Student Photo" class="card-avatar">
            <div class="status-badge status-graduated">Graduated</div>
        </div>
        <div class="card-body">
            <h3 class="entity-name">Alice Miller</h3>
            <div class="entity-id">ENR-2020-156</div>
        </div>
        <div class="card-actions">
            <button class="action-btn btn-view" onclick="viewEntity('student', 3)">
                <svg class="btn-icon" viewBox="0 0 24 24">
                    <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                </svg>
                View
            </button>
            <button class="action-btn btn-edit" onclick="editEntity('student', 3)">
                <svg class="btn-icon" viewBox="0 0 24 24">
                    <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                </svg>
                Edit
            </button>
            <button class="action-btn btn-delete" onclick="deleteEntity('student', 3)">
                <svg class="btn-icon" viewBox="0 0 24 24">
                    <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                </svg>
                Delete
            </button>
        </div>
    </div>
</div>

<script>
    // Placeholder functions for button actions - add your logic here later
    function viewEntity(type, id) {
        console.log(`View ${type} with ID: ${id}`);
        // Add your view logic here
    }

    function editEntity(type, id) {
        console.log(`Edit ${type} with ID: ${id}`);
        // Add your edit logic here
    }

    function deleteEntity(type, id) {
        console.log(`Delete ${type} with ID: ${id}`);
        // Add your delete logic here
    }
</script>
