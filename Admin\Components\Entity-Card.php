<style>
    .entity-card {
        background: #ffffff;
        border-radius: 1rem;
        padding: 1.5rem;
        margin: 1rem;
        width: 100%;
        max-width: 24rem;
        border: 0.0625rem solid rgba(226, 232, 240, 0.8);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.06);
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .entity-card:hover {
        transform: translateY(-0.25rem);
        box-shadow: 0 0.5rem 2rem rgba(79, 70, 229, 0.1);
        border-color: rgba(79, 70, 229, 0.2);
    }

    .profile-image {
        width: 3.5rem;
        height: 3.5rem;
        border-radius: 0.75rem;
        object-fit: cover;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 1.125rem;
        flex-shrink: 0;
        transition: all 0.3s ease;
    }

    .entity-card:hover .profile-image {
        transform: scale(1.05);
    }

    .card-info {
        flex: 1;
        min-width: 0;
    }

    .entity-name {
        font-size: 1.125rem;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 0.25rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .entity-id {
        font-size: 0.875rem;
        color: #64748b;
        font-weight: 500;
    }

    .menu-container {
        position: relative;
    }

    .menu-trigger {
        width: 2rem;
        height: 2rem;
        border: none;
        background: transparent;
        cursor: pointer;
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        color: #64748b;
    }

    .menu-trigger:hover {
        background: #f1f5f9;
        color: #1e293b;
    }

    .menu-dots {
        display: flex;
        flex-direction: column;
        gap: 0.125rem;
    }

    .menu-dots span {
        width: 0.25rem;
        height: 0.25rem;
        background: currentColor;
        border-radius: 50%;
    }

    .dropdown-menu {
        position: absolute;
        top: 100%;
        right: 0;
        background: white;
        border: 0.0625rem solid #e2e8f0;
        border-radius: 0.5rem;
        box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
        z-index: 10;
        min-width: 8rem;
        opacity: 0;
        visibility: hidden;
        transform: translateY(-0.5rem);
        transition: all 0.2s ease;
    }

    .dropdown-menu.active {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }

    .dropdown-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1rem;
        border: none;
        background: none;
        width: 100%;
        text-align: left;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        color: #374151;
    }

    .dropdown-item:first-child {
        border-radius: 0.5rem 0.5rem 0 0;
    }

    .dropdown-item:last-child {
        border-radius: 0 0 0.5rem 0.5rem;
    }

    .dropdown-item:hover {
        background: #f8fafc;
    }

    .dropdown-item.view {
        color: #2563eb;
    }

    .dropdown-item.edit {
        color: #059669;
    }

    .dropdown-item.delete {
        color: #dc2626;
    }

    .dropdown-item.delete:hover {
        background: #fef2f2;
    }

    /* Responsive Design */
    @media (max-width: 48rem) {
        .entity-card {
            margin: 0.5rem;
            padding: 1rem;
            max-width: none;
        }

        .profile-image {
            width: 3rem;
            height: 3rem;
            font-size: 1rem;
        }

        .entity-name {
            font-size: 1rem;
        }

        .entity-id {
            font-size: 0.8125rem;
        }
    }

    @media (max-width: 30rem) {
        .entity-card {
            padding: 0.75rem;
            gap: 0.75rem;
        }

        .profile-image {
            width: 2.5rem;
            height: 2.5rem;
            font-size: 0.875rem;
        }

        .entity-name {
            font-size: 0.9375rem;
        }

        .entity-id {
            font-size: 0.75rem;
        }

        .menu-trigger {
            width: 1.75rem;
            height: 1.75rem;
        }
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(1rem);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .entity-card {
        animation: slideInUp 0.4s ease-out;
    }
</style>

</style>

<!-- Single Entity Card Template -->
<div class="entity-card">
    <div class="profile-image">
        JD
    </div>
    <div class="card-info">
        <div class="entity-name">John Doe</div>
        <div class="entity-id">STU-2024-001</div>
    </div>
    <div class="menu-container">
        <button class="menu-trigger" onclick="toggleMenu(this)">
            <div class="menu-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </button>
        <div class="dropdown-menu">
            <button class="dropdown-item view" onclick="viewEntity()">
                👁️ View
            </button>
            <button class="dropdown-item edit" onclick="editEntity()">
                ✏️ Edit
            </button>
            <button class="dropdown-item delete" onclick="deleteEntity()">
                🗑️ Delete
            </button>
        </div>
    </div>
</div>

<script>
    function toggleMenu(trigger) {
        const menu = trigger.nextElementSibling;
        const isActive = menu.classList.contains('active');

        // Close all other menus
        document.querySelectorAll('.dropdown-menu.active').forEach(m => {
            m.classList.remove('active');
        });

        // Toggle current menu
        if (!isActive) {
            menu.classList.add('active');
        }
    }

    // Close menu when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.menu-container')) {
            document.querySelectorAll('.dropdown-menu.active').forEach(menu => {
                menu.classList.remove('active');
            });
        }
    });

    // Placeholder functions - add your logic here
    function viewEntity() {
        console.log('View entity');
        // Add your view logic here
    }

    function editEntity() {
        console.log('Edit entity');
        // Add your edit logic here
    }

    function deleteEntity() {
        console.log('Delete entity');
        // Add your delete logic here
    }
</script>
