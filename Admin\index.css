:root {
    /* Colors */
    --primary-color: #edf6ff;
    --secondary-color: #036ce2;
    --white: #ffffff;
    --text-color: #333333;
    --border-color: #e1e5e9;
    --success-color: #27ae60;
    --error-color: #e74c3c;

    /* Font */
    --primary-font: 'Poppins', sans-serif;

    /* Gradients */
    --bg-gradient: linear-gradient(135deg, #edf6ff 0%, #c3cfe2 100%);
    --header-gradient: linear-gradient(135deg, #036ce2, #0077ff);
    --button-gradient: linear-gradient(135deg, #036ce2, #0077ff);

    /* Shadows */
    --shadow: 0 0.625rem 1.875rem rgba(0, 0, 0, 0.1);
    --shadow-focus: 0 0 0 0.1875rem rgba(3, 108, 226, 0.1);

    /* Transition */
    --transition: all 0.3s ease;
}


* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 14px;
}

body {
    font-family: var(--primary-font);
    background-color: var(--primary-color);
    /* min-height: 100vh; */
    /* line-height: 1.6; */
    /* background-color: red; */
}